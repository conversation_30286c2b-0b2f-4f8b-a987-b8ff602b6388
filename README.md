# 鼠标自动化脚本

这是一个功能强大的鼠标自动化工具，可以录制和回放鼠标点击操作。

## 功能特点

1. **自定义屏幕区域选择** - 限制自动操作的空间范围
2. **鼠标点击录制** - 记录鼠标点击的位置和时间间隔
3. **全局快捷键控制** - 使用快捷键开启或关闭功能
4. **多脚本管理** - 可以存放多个脚本并自定义选择
5. **网络延迟自适应** - 自动检测和适应网络延迟
6. **GUI界面** - 直观的图形用户界面
7. **页面加载延迟检测** - 自动检测页面加载时间

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. **运行程序**
   ```bash
   python mouse_automation.py
   ```

2. **创建新脚本**
   - 点击"新建脚本"按钮
   - 输入脚本名称

3. **选择屏幕区域**（可选）
   - 点击"选择屏幕区域"按钮
   - 在屏幕上拖拽选择要操作的区域

4. **录制鼠标点击**
   - 选择一个脚本
   - 点击"开始录制"或按F9键
   - 在目标区域进行鼠标点击操作
   - 再次点击"停止录制"或按F9键结束录制

5. **执行脚本**
   - 选择要执行的脚本
   - 点击"执行脚本"或按F10键
   - 脚本将自动重复执行录制的点击操作

6. **调整延迟**
   - 手动设置网络延迟时间
   - 或点击"自动检测延迟"按钮

## 快捷键

- **F9**: 开始/停止录制
- **F10**: 开始/停止执行
- **ESC**: 紧急停止所有操作

## 注意事项

1. 请确保在安全的环境中使用此工具
2. 录制时请确保鼠标点击准确
3. 执行脚本时请注意不要干扰其他程序
4. 使用ESC键可以紧急停止所有操作
5. 脚本数据会自动保存到scripts.json文件中

## 文件说明

- `mouse_automation.py` - 主程序文件
- `requirements.txt` - 依赖包列表
- `scripts.json` - 脚本数据存储文件（自动生成）
- `README.md` - 使用说明文档

## 故障排除

如果遇到问题，请检查：
1. 是否正确安装了所有依赖包
2. 是否有足够的系统权限
3. 防火墙是否阻止了网络延迟检测功能
