@echo off
echo ========================================
echo 鼠标自动化脚本 - 依赖安装程序
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
echo.

pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 安装失败，尝试使用国内镜像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 现在可以运行 start.bat 来启动程序
echo 或者直接运行: python mouse_automation.py
echo.
pause
