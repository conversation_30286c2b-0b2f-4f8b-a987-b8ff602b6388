import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import simpledialog
import pyautogui
import json
import time
import threading
import keyboard
import requests
from PIL import Image, ImageTk
import cv2
import numpy as np
from datetime import datetime
import os

class MouseAutomationApp:
    def __init__(self, root):
        self.root = root
        self.root.title("鼠标自动化脚本")
        self.root.geometry("800x600")
        
        # 初始化变量
        self.scripts = {}
        self.current_script = None
        self.is_recording = False
        self.is_running = False
        self.selected_area = None
        self.network_delay = 0
        self.page_load_delay = 0
        
        # 创建GUI
        self.create_gui()
        
        # 加载脚本
        self.load_scripts()
        
        # 设置全局快捷键
        self.setup_hotkeys()
        
    def create_gui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 脚本管理区域
        script_frame = ttk.LabelFrame(main_frame, text="脚本管理", padding="5")
        script_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 脚本列表
        self.script_listbox = tk.Listbox(script_frame, height=6)
        self.script_listbox.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 脚本操作按钮
        ttk.Button(script_frame, text="新建脚本", command=self.new_script).grid(row=1, column=0, padx=5)
        ttk.Button(script_frame, text="删除脚本", command=self.delete_script).grid(row=1, column=1, padx=5)
        ttk.Button(script_frame, text="重命名", command=self.rename_script).grid(row=1, column=2, padx=5)
        
        # 区域选择
        area_frame = ttk.LabelFrame(main_frame, text="屏幕区域选择", padding="5")
        area_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(area_frame, text="选择屏幕区域", command=self.select_screen_area).grid(row=0, column=0, padx=5)
        self.area_label = ttk.Label(area_frame, text="未选择区域")
        self.area_label.grid(row=0, column=1, padx=5)
        
        # 录制控制
        record_frame = ttk.LabelFrame(main_frame, text="录制控制", padding="5")
        record_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.record_btn = ttk.Button(record_frame, text="开始录制", command=self.toggle_recording)
        self.record_btn.grid(row=0, column=0, padx=5)
        
        self.status_label = ttk.Label(record_frame, text="状态: 待机")
        self.status_label.grid(row=0, column=1, padx=5)
        
        # 执行控制
        execute_frame = ttk.LabelFrame(main_frame, text="执行控制", padding="5")
        execute_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.execute_btn = ttk.Button(execute_frame, text="执行脚本", command=self.toggle_execution)
        self.execute_btn.grid(row=0, column=0, padx=5)
        
        # 延迟设置
        delay_frame = ttk.LabelFrame(main_frame, text="延迟设置", padding="5")
        delay_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(delay_frame, text="网络延迟(ms):").grid(row=0, column=0)
        self.network_delay_var = tk.StringVar(value="100")
        ttk.Entry(delay_frame, textvariable=self.network_delay_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Button(delay_frame, text="自动检测延迟", command=self.auto_detect_delay).grid(row=0, column=2, padx=5)
        
        # 快捷键说明
        hotkey_frame = ttk.LabelFrame(main_frame, text="快捷键", padding="5")
        hotkey_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(hotkey_frame, text="F9: 开始/停止录制  F10: 开始/停止执行  ESC: 紧急停止").grid(row=0, column=0)
        
    def setup_hotkeys(self):
        """设置全局快捷键"""
        keyboard.add_hotkey('f9', self.toggle_recording)
        keyboard.add_hotkey('f10', self.toggle_execution)
        keyboard.add_hotkey('esc', self.emergency_stop)
        
    def new_script(self):
        """创建新脚本"""
        name = tk.simpledialog.askstring("新建脚本", "请输入脚本名称:")
        if name and name not in self.scripts:
            self.scripts[name] = {
                'clicks': [],
                'area': None,
                'created': datetime.now().isoformat()
            }
            self.update_script_list()
            self.save_scripts()
            
    def delete_script(self):
        """删除选中的脚本"""
        selection = self.script_listbox.curselection()
        if selection:
            script_name = self.script_listbox.get(selection[0])
            if messagebox.askyesno("确认删除", f"确定要删除脚本 '{script_name}' 吗？"):
                del self.scripts[script_name]
                self.update_script_list()
                self.save_scripts()
                
    def rename_script(self):
        """重命名脚本"""
        selection = self.script_listbox.curselection()
        if selection:
            old_name = self.script_listbox.get(selection[0])
            new_name = tk.simpledialog.askstring("重命名脚本", "请输入新名称:", initialvalue=old_name)
            if new_name and new_name != old_name and new_name not in self.scripts:
                self.scripts[new_name] = self.scripts.pop(old_name)
                self.update_script_list()
                self.save_scripts()
                
    def update_script_list(self):
        """更新脚本列表显示"""
        self.script_listbox.delete(0, tk.END)
        for script_name in self.scripts.keys():
            self.script_listbox.insert(tk.END, script_name)
            
    def select_screen_area(self):
        """选择屏幕区域"""
        self.root.withdraw()  # 隐藏主窗口
        time.sleep(0.5)
        
        # 创建全屏截图
        screenshot = pyautogui.screenshot()
        
        # 创建选择窗口
        self.area_window = tk.Toplevel()
        self.area_window.attributes('-fullscreen', True)
        self.area_window.attributes('-alpha', 0.3)
        self.area_window.configure(bg='black')
        
        # 绑定鼠标事件
        self.start_pos = None
        self.area_window.bind('<Button-1>', self.on_area_start)
        self.area_window.bind('<B1-Motion>', self.on_area_drag)
        self.area_window.bind('<ButtonRelease-1>', self.on_area_end)
        
    def on_area_start(self, event):
        """开始选择区域"""
        self.start_pos = (event.x_root, event.y_root)
        
    def on_area_drag(self, event):
        """拖拽选择区域"""
        pass
        
    def on_area_end(self, event):
        """结束选择区域"""
        if self.start_pos:
            end_pos = (event.x_root, event.y_root)
            self.selected_area = {
                'x1': min(self.start_pos[0], end_pos[0]),
                'y1': min(self.start_pos[1], end_pos[1]),
                'x2': max(self.start_pos[0], end_pos[0]),
                'y2': max(self.start_pos[1], end_pos[1])
            }
            self.area_label.config(text=f"区域: {self.selected_area}")
            
        self.area_window.destroy()
        self.root.deiconify()  # 显示主窗口
        
    def toggle_recording(self):
        """切换录制状态"""
        if not self.is_recording:
            selection = self.script_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请先选择一个脚本")
                return
                
            self.current_script = self.script_listbox.get(selection[0])
            self.scripts[self.current_script]['clicks'] = []  # 清空之前的录制
            self.is_recording = True
            self.record_btn.config(text="停止录制")
            self.status_label.config(text="状态: 录制中...")
            
            # 开始录制线程
            self.record_thread = threading.Thread(target=self.record_clicks)
            self.record_thread.daemon = True
            self.record_thread.start()
        else:
            self.is_recording = False
            self.record_btn.config(text="开始录制")
            self.status_label.config(text="状态: 录制完成")
            self.save_scripts()
            
    def record_clicks(self):
        """录制鼠标点击"""
        print("录制开始，请在目标区域进行鼠标点击...")
        last_click_time = 0

        while self.is_recording:
            try:
                # 获取当前鼠标位置
                x, y = pyautogui.position()

                # 检查鼠标是否被按下
                if pyautogui.mouseInfo() is not None:
                    current_time = time.time()

                    # 避免重复记录（间隔至少0.2秒）
                    if current_time - last_click_time > 0.2:
                        # 检查是否在选定区域内
                        if self.selected_area:
                            if not (self.selected_area['x1'] <= x <= self.selected_area['x2'] and
                                   self.selected_area['y1'] <= y <= self.selected_area['y2']):
                                time.sleep(0.05)
                                continue

                        # 记录点击位置
                        click_data = {
                            'x': x,
                            'y': y,
                            'timestamp': current_time
                        }
                        self.scripts[self.current_script]['clicks'].append(click_data)
                        last_click_time = current_time
                        print(f"记录点击: ({x}, {y})")

            except Exception as e:
                print(f"录制错误: {e}")

            time.sleep(0.05)

    def toggle_execution(self):
        """切换执行状态"""
        if not self.is_running:
            selection = self.script_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请先选择一个脚本")
                return

            script_name = self.script_listbox.get(selection[0])
            if not self.scripts[script_name]['clicks']:
                messagebox.showwarning("警告", "该脚本没有录制的点击")
                return

            self.is_running = True
            self.execute_btn.config(text="停止执行")
            self.status_label.config(text="状态: 执行中...")

            # 开始执行线程
            self.execute_thread = threading.Thread(target=self.execute_script, args=(script_name,))
            self.execute_thread.daemon = True
            self.execute_thread.start()
        else:
            self.is_running = False
            self.execute_btn.config(text="执行脚本")
            self.status_label.config(text="状态: 执行停止")

    def execute_script(self, script_name):
        """执行脚本"""
        clicks = self.scripts[script_name]['clicks']
        network_delay = int(self.network_delay_var.get()) / 1000.0

        while self.is_running:
            for i, click in enumerate(clicks):
                if not self.is_running:
                    break

                # 移动并点击
                pyautogui.click(click['x'], click['y'])

                # 计算延迟
                if i < len(clicks) - 1:
                    original_delay = clicks[i+1]['timestamp'] - click['timestamp']
                    adjusted_delay = original_delay + network_delay + self.page_load_delay
                    time.sleep(max(0.1, adjusted_delay))
                else:
                    time.sleep(1)  # 循环间隔

    def auto_detect_delay(self):
        """自动检测网络延迟"""
        try:
            # 检测网络延迟
            start_time = time.time()
            response = requests.get('https://www.baidu.com', timeout=5)
            if response.status_code == 200:
                network_delay = (time.time() - start_time) * 1000
                self.network_delay_var.set(str(int(network_delay)))
                messagebox.showinfo("延迟检测", f"网络延迟: {int(network_delay)}ms")
            else:
                messagebox.showwarning("警告", "网络连接异常")

        except Exception as e:
            messagebox.showerror("错误", f"延迟检测失败: {e}")

    def emergency_stop(self):
        """紧急停止所有操作"""
        self.is_recording = False
        self.is_running = False
        self.record_btn.config(text="开始录制")
        self.execute_btn.config(text="执行脚本")
        self.status_label.config(text="状态: 紧急停止")

    def save_scripts(self):
        """保存脚本到文件"""
        try:
            with open('scripts.json', 'w', encoding='utf-8') as f:
                json.dump(self.scripts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存脚本失败: {e}")

    def load_scripts(self):
        """从文件加载脚本"""
        try:
            if os.path.exists('scripts.json'):
                with open('scripts.json', 'r', encoding='utf-8') as f:
                    self.scripts = json.load(f)
                self.update_script_list()
        except Exception as e:
            print(f"加载脚本失败: {e}")

    def on_closing(self):
        """程序关闭时的处理"""
        self.emergency_stop()
        self.save_scripts()
        self.root.destroy()

def main():
    # 设置pyautogui安全设置
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1

    root = tk.Tk()
    app = MouseAutomationApp(root)

    # 绑定关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    root.mainloop()

if __name__ == "__main__":
    main()
