"""
改进的鼠标录制模块
使用pynput库来更准确地检测鼠标点击事件
"""

try:
    from pynput import mouse
    from pynput.mouse import Listener
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("pynput库未安装，将使用基础录制方法")

import time
import threading

class MouseRecorder:
    def __init__(self):
        self.is_recording = False
        self.clicks = []
        self.selected_area = None
        self.listener = None
        
    def start_recording(self, selected_area=None):
        """开始录制鼠标点击"""
        self.is_recording = True
        self.clicks = []
        self.selected_area = selected_area
        
        if PYNPUT_AVAILABLE:
            # 使用pynput进行精确录制
            self.listener = Listener(on_click=self._on_click)
            self.listener.start()
        else:
            # 使用基础方法
            self.record_thread = threading.Thread(target=self._basic_record)
            self.record_thread.daemon = True
            self.record_thread.start()
            
    def stop_recording(self):
        """停止录制"""
        self.is_recording = False
        if self.listener:
            self.listener.stop()
            
    def _on_click(self, x, y, button, pressed):
        """pynput点击事件处理"""
        if not self.is_recording or not pressed:
            return
            
        # 检查是否在选定区域内
        if self.selected_area:
            if not (self.selected_area['x1'] <= x <= self.selected_area['x2'] and 
                   self.selected_area['y1'] <= y <= self.selected_area['y2']):
                return
                
        # 记录点击
        click_data = {
            'x': x,
            'y': y,
            'timestamp': time.time(),
            'button': str(button)
        }
        self.clicks.append(click_data)
        print(f"记录点击: ({x}, {y}) - {button}")
        
    def _basic_record(self):
        """基础录制方法（备用）"""
        import pyautogui
        last_click_time = 0
        
        while self.is_recording:
            try:
                x, y = pyautogui.position()
                current_time = time.time()
                
                # 简单的点击检测（需要用户手动触发）
                if current_time - last_click_time > 0.5:
                    # 这里需要其他方式来检测点击
                    # 可以通过键盘快捷键来标记点击位置
                    pass
                    
            except Exception as e:
                print(f"录制错误: {e}")
                
            time.sleep(0.1)
            
    def get_clicks(self):
        """获取录制的点击数据"""
        return self.clicks
